{"name": "@formily/element-plus", "version": "1.0.0-beta.5", "license": "MIT", "main": "lib", "types": "lib/index.d.ts", "engines": {"npm": ">=3.0.0"}, "module": "esm", "umd:main": "dist/formily.element-plus.umd.production.js", "unpkg": "dist/formily.element-plus.umd.production.js", "jsdelivr": "dist/formily.element-plus.umd.production.js", "jsnext:main": "esm", "sideEffects": ["dist/*", "esm/*.js", "lib/*.js", "src/*.ts", "*.less", "*.scss", "**/*/style.js"], "scripts": {"start": "vuepress dev docs", "clean": "rimraf -rf lib esm dist", "build": "formily-tpl build", "create:style": "ts-node create-style", "build:docs": "vuepress build docs"}, "repository": {"type": "git", "url": "git+https://github.com/formilyjs/element.git"}, "bugs": {"url": "https://github.com/formilyjs/element/issues"}, "homepage": "https://github.com/formilyjs/element#readme", "publishConfig": {"access": "public"}, "peerDependencies": {"vue": "^3.2.24"}, "dependencies": {"@element-plus/icons-vue": "^2.0.4", "@formily/core": "^2.1.5", "@formily/grid": "^2.1.5", "@formily/json-schema": "^2.1.5", "@formily/reactive": "^2.1.5", "@formily/reactive-vue": "^2.1.5", "@formily/shared": "^2.1.5", "@formily/validator": "^2.1.5", "@formily/vue": "^2.1.5", "element-plus": "^2.2.5", "resize-observer-polyfill": "^1.5.1", "vue-slicksort": "^1.2.0", "vuedraggable": "^4.1.0"}}
@use 'sass:math';
@import '../__builtins__/styles/common.scss';
@import './var.scss';
@import './grid.scss';
@import './animation.scss';

.#{$form-item-prefix} {
  display: flex;
  margin-bottom: $--form-item-margin-bottom;
  position: relative;
  line-height: $--form-item-medium-line-height;
  font-size: $--form-font-size;

  &-label * {
    line-height: $--form-item-medium-line-height;
  }

  &-label-content {
    min-height: $--form-item-medium-line-height;
  }

  &-content-component {
    line-height: $--form-item-medium-line-height;
  }

  .#{$namespace}-input,
  .#{$namespace}-input-number,
  .#{$namespace}-input-number.is-controls-right,
  .#{$namespace}-select,
  .#{$namespace}-cascader,
  .#{$namespace}-date-editor--daterange,
  .#{$namespace}-date-editor--timerange,
  .#{$namespace}-date-editor--datetimerange,
  .#{$namespace}-date-editor.#{$namespace}-input,
  .#{$namespace}-date-editor.#{$namespace}-input__inner,
  .#{$namespace}-tree-select {
    width: 100%;
  }

  .#{$namespace}-input-group {
    vertical-align: top;
  }
}

.#{$form-item-prefix}-label {
  position: relative;
  display: flex;

  &-content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-tooltip {
    cursor: help;

    * {
      cursor: help;
    }

    label {
      border-bottom: 1px dashed currentColor;
    }
  }
}

.#{$form-item-prefix}-label label {
  color: var(--el-text-color-regular);
}

.#{$form-item-prefix}-label-align-left {
  > .#{$form-item-prefix}-label {
    justify-content: flex-start;
  }
}

.#{$form-item-prefix}-label-align-right {
  > .#{$form-item-prefix}-label {
    justify-content: flex-end;
  }
}

.#{$form-item-prefix}-label-wrap {
  .#{$form-item-prefix}-label {
    label {
      white-space: pre-line;
    }
  }
}

.#{$form-item-prefix}-feedback-layout-terse {
  margin-bottom: 8px;

  &.#{$form-item-prefix}-feedback-has-text:not(.#{$form-item-prefix}-inset) {
    margin-bottom: 0;
  }
}

.#{$form-item-prefix}-feedback-layout-loose {
  margin-bottom: $--form-error-line-height;

  &.#{$form-item-prefix}-feedback-has-text:not(.#{$form-item-prefix}-inset) {
    margin-bottom: 0;
  }
}

.#{$form-item-prefix}-feedback-layout-none {
  margin-bottom: 0;

  &.#{$form-item-prefix}-feedback-has-text:not(.#{$form-item-prefix}-inset) {
    margin-bottom: 0;
  }
}

.#{$form-item-prefix}-control {
  width: 100%;
  flex: 1;

  .#{$form-item-prefix}-control-content {
    display: flex;

    .#{$form-item-prefix}-control-content-component {
      width: 100%;
      min-height: $--form-item-medium-line-height;
      line-height: $--form-item-medium-line-height;

      &-has-feedback-icon {
        flex: 1;
        position: relative;
        display: flex;
        align-items: center;
      }
    }

    .#{$form-item-prefix}-addon-before {
      margin-right: 8px;
      display: inline-flex;
      align-items: center;
      min-height: $--form-item-medium-line-height;
      flex-shrink: 0;
    }

    .#{$form-item-prefix}-addon-after {
      margin-left: 8px;
      display: inline-flex;
      align-items: center;
      min-height: $--form-item-medium-line-height;
      flex-shrink: 0;
    }
  }
}

.#{$form-item-prefix}-size-small {
  font-size: $--font-size-extra-small;

  .#{$form-item-prefix}-label * {
    line-height: $--form-item-small-line-height;
  }

  .#{$form-item-prefix}-label-content {
    min-height: $--form-item-small-line-height;
  }

  .#{$form-item-prefix}-control-content {
    .#{$form-item-prefix}-control-content-component {
      line-height: $--form-item-small-line-height;
      min-height: $--form-item-small-line-height;
    }
  }

  .#{$form-item-prefix}-help,
  .#{$form-item-prefix}-extra {
    min-height: $--form-error-line-height;
  }

  .#{$form-item-prefix}-control-content {
    min-height: $--form-item-small-line-height;
  }

  .#{$form-item-prefix}-label > label {
    height: $--form-item-small-line-height;
  }

  .#{$namespace}-input {
    input {
      height: $--form-item-small-line-height;
      line-height: $--form-item-small-line-height;
    }
  }

  .#{$namespace}-input-number {
    line-height: $--form-item-small-line-height;
    &.is-controls-right {
      .#{$namespace}-input-number__increase,
      .#{$namespace}-input-number__decrease {
        line-height: calc($--form-item-small-line-height / 2);
        height: calc($--form-item-small-line-height / 2);
        font-size: $--font-size-extra-small;
        box-sizing: border-box;
      }
    }
  }
}

.#{$form-item-prefix}-size-large {
  font-size: $--font-size-medium;

  .#{$form-item-prefix}-label * {
    line-height: $--form-item-large-line-height;
  }

  .#{$form-item-prefix}-label-content {
    min-height: $--form-item-large-line-height;
  }

  .#{$form-item-prefix}-control-content {
    .#{$form-item-prefix}-control-content-component {
      line-height: $--form-item-large-line-height;
      min-height: $--form-item-large-line-height;
    }
  }

  .#{$form-item-prefix}-help,
  .#{$form-item-prefix}-extra {
    min-height: $--form-error-line-height;
  }

  .#{$form-item-prefix}-control-content {
    min-height: $--form-item-large-line-height;
  }

  .#{$namespace}-input {
    input {
      height: $--form-item-large-line-height;
      line-height: $--form-item-large-line-height;
    }
  }

  .#{$namespace}-select {
    input {
      height: $--form-item-large-line-height !important;
      line-height: $--form-item-large-line-height;
    }
  }

  .#{$namespace}-select__tags .el-tag {
    height: $--form-item-large-line-height - 12px;
    line-height: $--form-item-large-line-height - 12px;
  }

  .#{$namespace}-input-number {
    line-height: $--form-item-large-line-height;
    &.is-controls-right {
      .#{$namespace}-input-number__increase,
      .#{$namespace}-input-number__decrease {
        line-height: calc($--form-item-large-line-height / 2) - 1;
        font-size: $--font-size-medium;
      }
    }
  }
}

.#{$form-item-prefix} {
  &-layout-vertical {
    display: block;

    .#{$form-item-prefix}-label * {
      line-height: $--form-item-label-top-line-height;
    }

    .#{$form-item-prefix}-label-content {
      min-height: $--form-item-label-top-line-height;
    }
  }
}

.#{$form-item-prefix}-feedback-layout-popover {
  margin-bottom: 8px;
}

.#{$form-item-prefix}-label-tooltip {
  margin-left: 4px;
  color: $--color-text-secondary;
  display: flex;
  align-items: center;
  height: $--form-item-medium-line-height;
  cursor: pointer;
  i {
    line-height: 1;
  }
}

.#{$form-item-prefix}-control-align-left {
  .#{$form-item-prefix}-control-content {
    justify-content: flex-start;
  }
}

.#{$form-item-prefix}-control-align-right {
  .#{$form-item-prefix}-control-content {
    justify-content: flex-end;
  }
}

.#{$form-item-prefix}-control-wrap {
  .#{$form-item-prefix}-control {
    white-space: pre-line;
  }
}

.#{$form-item-prefix}-asterisk {
  color: $--color-danger;
  margin-right: 4px;
  display: inline-block;
  font-family: SimSun, sans-serif;
}

.#{$form-item-prefix}-colon {
  margin-left: 2px;
  margin-right: 8px;
}

.#{$form-item-prefix}-help,
.#{$form-item-prefix}-extra {
  clear: both;
  min-height: $--form-error-line-height;
  line-height: $--form-error-line-height;
  color: $--color-text-secondary;
  transition: $--color-transition-base;
  padding-top: 0;
}

.#{$form-item-prefix}-fullness {
  > .#{$form-item-prefix}-control {
    > .#{$form-item-prefix}-control-content {
      > .#{$form-item-prefix}-control-content-component {
        > *:first-child {
          width: 100%;
        }
      }
    }
  }
}

.#{$form-item-prefix}-control-content-component-has-feedback-icon {
  border-radius: $--border-radius-base;
  border: $--border-base;
  padding-right: 8px;
  transition: $--all-transition;
  touch-action: manipulation;
  outline: none;

  .#{$namespace}-input-number,
  .#{$namespace}-date-editor .#{$namespace}-input__inner,
  .#{$namespace}-select .#{$namespace}-input__inner,
  .#{$namespace}-input .#{$namespace}-input__inner {
    border: none !important;
    box-shadow: none !important;
  }
  .#{$namespace}-input-number.is-controls-right .#{$namespace}-input__inner {
    padding-right: 40px;
  }
  .#{$namespace}-input-number.is-controls-right
    .#{$namespace}-input-number__increase {
    top: 0;
    right: 8px;
    border-right: $--border-base;
  }
  .#{$namespace}-input-number.is-controls-right
    .#{$namespace}-input-number__decrease {
    bottom: 0;
    right: 8px;
    border-right: $--border-base;
  }
}

.#{$form-item-prefix} {
  &:hover {
    .#{$form-item-prefix}-control-content-component-has-feedback-icon {
      @include hover;
    }
  }
}

.#{$form-item-prefix}-active {
  .#{$form-item-prefix}-control-content-component-has-feedback-icon {
    @include active;
  }
}

.#{$form-item-prefix}-error {
  & .#{$namespace}-input__inner,
  & .#{$namespace}-textarea__inner {
    &,
    &.hover {
      border-color: $--color-danger;
    }
  }

  & .#{$namespace}-input__inner,
  & .#{$namespace}-textarea__inner {
    &:focus {
      border-color: $--color-danger;
    }
  }

  & .#{$namespace}-input-group__append,
  & .#{$namespace}-input-group__prepend {
    & .#{$namespace}-input__inner {
      border-color: transparent;
    }
  }
  .#{$namespace}-input__validateIcon {
    color: $--color-danger !important;
  }
}

.#{$form-item-prefix}-error-help,
.#{$form-item-prefix}-warning-help,
.#{$form-item-prefix}-success-help {
  i {
    margin-right: 8px;
  }
}

.#{$form-item-prefix}-error-help {
  color: $--color-danger;
}

.#{$form-item-prefix}-warning-help {
  color: $--color-warning;
}

.#{$form-item-prefix}-success-help {
  color: $--color-success;
}

.#{$form-item-prefix}-warning {
  & .#{$namespace}-input__inner,
  & .#{$namespace}-textarea__inner {
    &,
    &.hover {
      border-color: $--color-warning;
    }
  }

  & .#{$namespace}-input__inner,
  & .#{$namespace}-textarea__inner {
    &:focus {
      border-color: $--color-warning;
    }
  }

  & .#{$namespace}-input-group__append,
  & .#{$namespace}-input-group__prepend {
    & .#{$namespace}-input__inner {
      border-color: transparent;
    }
  }
  .#{$namespace}-input__validateIcon {
    color: $--color-warning !important;
  }
}

.#{$form-item-prefix}-success {
  & .#{$namespace}-input__inner,
  & .#{$namespace}-textarea__inner {
    &,
    &.hover {
      border-color: $--color-success;
    }
  }

  & .#{$namespace}-input__inner,
  & .#{$namespace}-textarea__inner {
    &:focus {
      border-color: $--color-success;
    }
  }

  & .#{$namespace}-input-group__append,
  & .#{$namespace}-input-group__prepend {
    & .#{$namespace}-input__inner {
      border-color: transparent;
    }
  }
  .#{$namespace}-input__validateIcon {
    color: $--color-success !important;
  }
}

.#{$form-item-prefix}-bordered-none {
  .#{$namespace}-input__inner {
    border: none !important;
  }

  .#{$namespace}-input-number__decrease,
  .#{$namespace}-input-number__increase {
    border: none !important;
    background: transparent !important;
  }
}

.#{$form-item-prefix}-inset {
  border-radius: $--border-radius-base;
  border: $--border-base;
  padding-left: 12px;
  transition: 0.3s all;

  &:hover {
    @include hover;
  }
}

.#{$form-item-prefix}-inset-active {
  @include active;
}

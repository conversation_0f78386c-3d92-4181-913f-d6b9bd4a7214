export const FontWeight = (
  <path d="M693.505588,190 C724.805588,190 746.305588,207.1 758.805588,234.8 L758.805588,234.8 L991.505588,753.9 C996.005588,762 997.805588,770 997.805588,777.2 C997.805588,806.7 975.405588,830 945.905588,830 C920.005588,830 902.105588,814.8 892.105588,791.6 L892.105588,791.6 L841.105588,674.3 L582.273,674.3 L630.105588,784 C637.805588,801.7 629.705588,822.3 612.005588,830.1 C607.405588,832.1 602.705588,833 598.005588,833 C584.505588,833 571.605588,825.1 565.905588,812 L521.33,709.704 L484.005588,795.1 C475.194588,817.276 456.680598,829.74901 433.703232,829.996253 L433.005588,830 C404.405588,830 382.005588,807.6 382.005588,779 C382.005588,770.9 384.705588,762.9 389.205588,753.9 L389.205588,753.9 L430.85,661 L162.005588,661 C161.540572,661 161.077646,660.990886 160.61698,660.972827 L92.9055878,812.3 C85.0055878,829.9 64.3055878,837.9 46.7055878,830 C29.1055878,822.1 21.2055878,801.4 29.0055878,783.8 L273.305588,237.8 C287.905588,205.1 326.505588,190.4 359.205588,205 C374.005588,211.6 385.805588,223.5 392.205588,238.4 L504.67,496.325 L621.905588,234.8 C634.280588,207.278 656.358578,190.34108 687.169259,190.005094 L688.105588,190 Z M332.605588,276.6 L191.926,591 L462.229,591 L465.969,582.657 L332.605588,276.6 Z M689.005588,323.4 L578.905588,576.7 L799.105588,576.7 L689.005588,323.4 Z"></path>
)

export const FontStyle = (
  <path d="M352.741259,192 C384.06993,192 405.552448,209.006993 418.083916,236.755245 L418.083916,236.755245 L650.811189,755.916084 C655.286713,763.972028 657.076923,772.027972 657.076923,779.188811 C657.076923,808.727273 634.699301,832 605.160839,832 C579.202797,832 561.300699,816.783217 551.454545,793.51049 L551.454545,793.51049 L500.433566,676.251748 L196.097902,676.251748 L143.286713,797.090909 C134.335664,819.468531 115.538462,832 92.2657343,832 C63.6223776,832 41.2447552,809.622378 41.2447552,780.979021 C41.2447552,772.923077 43.9300699,764.867133 48.4055944,755.916084 L48.4055944,755.916084 L281.132867,236.755245 C293.664336,209.006993 316.041958,192 347.370629,192 L347.370629,192 Z M833.641026,492 C882.835165,492 919.575092,505.076923 942.615385,528.739927 C966.901099,552.40293 978.10989,587.274725 978.10989,630.241758 L978.10989,630.241758 L978.10989,792.14652 C978.10989,812.695971 961.919414,828.263736 941.369963,828.263736 C919.575092,828.263736 904.630037,813.318681 904.630037,796.505495 L904.630037,796.505495 L904.630037,784.051282 C882.212454,810.827839 847.96337,832 797.52381,832 C735.875458,832 681.076923,796.505495 681.076923,730.498168 L681.076923,730.498168 L681.076923,729.252747 C681.076923,658.263736 736.498168,623.391941 816.827839,623.391941 C853.567766,623.391941 879.721612,628.996337 905.252747,637.091575 L905.252747,637.091575 L905.252747,628.996337 C905.252747,582.29304 876.608059,557.384615 823.677656,557.384615 C795.032967,557.384615 771.369963,562.3663 750.820513,570.461538 C746.461538,571.70696 742.725275,572.32967 738.989011,572.32967 C721.553114,572.32967 707.230769,558.630037 707.230769,541.194139 C707.230769,527.494505 716.571429,515.663004 727.78022,511.304029 C758.915751,499.472527 790.673993,492 833.641026,492 Z M831.772894,671.340659 C783.201465,671.340659 754.556777,691.89011 754.556777,726.139194 L754.556777,726.139194 L754.556777,727.384615 C754.556777,759.142857 782.578755,777.201465 818.695971,777.201465 C868.512821,777.201465 906.498168,748.556777 906.498168,706.835165 L906.498168,706.835165 L906.498168,684.417582 C887.194139,676.945055 861.663004,671.340659 831.772894,671.340659 Z M348.265734,325.370629 L238.167832,578.685315 L458.363636,578.685315 L348.265734,325.370629 Z"></path>
)

export const NormalFontStyle = (
  <path d="M662,100 C684.09139,100 702,117.90861 702,140 C702,162.09139 684.09139,180 662,180 L551.95089,180.000411 C551.983502,180.662832 552,181.329489 552,182 L552,842 C552,842.670849 551.983486,843.33784 551.950841,844.00059 L662,844 C684.09139,844 702,861.90861 702,884 C702,906.09139 684.09139,924 662,924 L362,924 C339.90861,924 322,906.09139 322,884 C322,861.90861 339.90861,844 362,844 L472.049159,844.00059 C472.016514,843.33784 472,842.670849 472,842 L472,182 C472,181.329489 472.016498,180.662832 472.04911,180.000411 L362,180 C339.90861,180 322,162.09139 322,140 C322,117.90861 339.90861,100 362,100 L662,100 Z"></path>
)

export const ItalicFontStyle = (
  <path d="M712,100 C734.09139,100 752,117.90861 752,140 C752,162.09139 734.09139,180 712,180 L597.596446,180.000524 C598.053921,183.498391 598.05267,187.115299 597.537846,190.778461 L505.733,844 L616,844 C638.09139,844 656,861.90861 656,884 C656,906.09139 638.09139,924 616,924 L316,924 C293.90861,924 276,906.09139 276,884 C276,861.90861 293.90861,844 316,844 L426.403684,844.000476 C425.946079,840.502298 425.947281,836.88505 426.462154,833.221539 L518.266,180 L412,180 C389.90861,180 372,162.09139 372,140 C372,117.90861 389.90861,100 412,100 L712,100 Z"></path>
)

export const FontColor = (
  <path d="M685.986014,192 C717.314685,192 738.797203,209.006993 751.328671,236.755245 L751.328671,236.755245 L984.055944,755.916084 C988.531469,763.972028 990.321678,772.027972 990.321678,779.188811 C990.321678,808.727273 967.944056,832 938.405594,832 C912.447552,832 894.545455,816.783217 884.699301,793.51049 L884.699301,793.51049 L833.678322,676.251748 L529.342657,676.251748 L476.531469,797.090909 C467.58042,819.468531 448.783217,832 425.51049,832 C396.867133,832 374.48951,809.622378 374.48951,780.979021 C374.48951,772.923077 377.174825,764.867133 381.65035,755.916084 L381.65035,755.916084 L614.377622,236.755245 C626.909091,209.006993 649.286713,192 680.615385,192 L680.615385,192 Z M242.396756,312 C344.87566,419.590227 396.115112,501.684108 396.115112,558.281644 C396.115112,643.177948 327.29306,712 242.396756,712 C157.500452,712 88.6784003,643.177948 88.6784003,558.281644 C88.6784003,501.684108 139.917852,419.590227 242.396756,312 Z M681.51049,325.370629 L571.412587,578.685315 L791.608392,578.685315 L681.51049,325.370629 Z"></path>
)

export const FontSize = (
  <path d="M100,189.5 C102.155215,189.5 104.27062,189.67045 106.333468,189.998605 L580,190 L580,190 C602.09139,190 620,207.90861 620,230 L620,280 C620,302.09139 602.09139,320 580,320 C557.90861,320 540,302.09139 540,280 L540,270 L380,270 L380,753 L500,753 C522.09139,753 540,770.90861 540,793 C540,815.09139 522.09139,833 500,833 L180,833 C157.90861,833 140,815.09139 140,793 C140,770.90861 157.90861,753 180,753 L300,753 L300,269.999 L140,269.999 L140,279.5 C140,301.59139 122.09139,319.5 100,319.5 C77.90861,319.5 60,301.59139 60,279.5 L60,229.5 C60,207.40861 77.90861,189.5 100,189.5 Z M652,833 C629.90861,833 612,815.09139 612,793 C612,770.90861 629.90861,753 652,753 L712,753 L712,541 L620,541 L620,551 C620,573.09139 602.09139,591 580,591 C557.90861,591 540,573.09139 540,551 L540,501 C540,478.90861 557.90861,461 580,461 L924,461 L924,461 C946.09139,461 964,478.90861 964,501 L964,551 C964,573.09139 946.09139,591 924,591 C901.90861,591 884,573.09139 884,551 L884,541 L792,541 L792,753 L852,753 C874.09139,753 892,770.90861 892,793 C892,815.09139 874.09139,833 852,833 L652,833 Z"></path>
)

export const LineHeight = (
  <path d="M919.461538,904 C941.552928,904 959.461538,921.90861 959.461538,944 C959.461538,966.09139 941.552928,984 919.461538,984 L487.461538,984 C465.370148,984 447.461538,966.09139 447.461538,944 C447.461538,921.90861 465.370148,904 487.461538,904 L919.461538,904 Z M254.74581,426.382395 L367.882895,539.51948 C383.503866,555.140452 383.503866,580.467051 367.882895,596.088023 C352.261923,611.708995 326.935324,611.708995 311.314352,596.088023 L266.461453,551.235 L266.461453,847.431 L311.314352,802.578644 C326.779114,787.113882 351.756362,786.959234 367.411373,802.114701 L367.882895,802.578644 C383.503866,818.199615 383.503866,843.526215 367.882895,859.147186 L367.882895,859.147186 L254.74581,972.284271 C239.124838,987.905243 213.798239,987.905243 198.177267,972.284271 L198.177267,972.284271 L85.0401822,859.147186 C69.4192106,843.526215 69.4192106,818.199615 85.0401822,802.578644 C100.661154,786.957672 125.987753,786.957672 141.608725,802.578644 L141.608725,802.578644 L186.461453,847.432 L186.461453,551.234 L141.608725,596.088023 C125.987753,611.708995 100.661154,611.708995 85.0401822,596.088023 C69.4192106,580.467051 69.4192106,555.140452 85.0401822,539.51948 L198.177267,426.382395 C213.798239,410.761424 239.124838,410.761424 254.74581,426.382395 Z M707.509158,529.333333 C756.703297,529.333333 793.443223,542.410256 816.483516,566.07326 C840.769231,589.736264 851.978022,624.608059 851.978022,667.575092 L851.978022,667.575092 L851.978022,829.479853 C851.978022,850.029304 835.787546,865.59707 815.238095,865.59707 C793.443223,865.59707 778.498168,850.652015 778.498168,833.838828 L778.498168,833.838828 L778.498168,821.384615 C756.080586,848.161172 721.831502,869.333333 671.391941,869.333333 C609.74359,869.333333 554.945055,833.838828 554.945055,767.831502 L554.945055,767.831502 L554.945055,766.586081 C554.945055,695.59707 610.3663,660.725275 690.695971,660.725275 C727.435897,660.725275 753.589744,666.32967 779.120879,674.424908 L779.120879,674.424908 L779.120879,666.32967 C779.120879,619.626374 750.47619,594.717949 697.545788,594.717949 C668.901099,594.717949 645.238095,599.699634 624.688645,607.794872 C620.32967,609.040293 616.593407,609.663004 612.857143,609.663004 C595.421245,609.663004 581.098901,595.96337 581.098901,578.527473 C581.098901,564.827839 590.43956,552.996337 601.648352,548.637363 C632.783883,536.805861 664.542125,529.333333 707.509158,529.333333 Z M705.641026,708.673993 C657.069597,708.673993 628.424908,729.223443 628.424908,763.472527 L628.424908,763.472527 L628.424908,764.717949 C628.424908,796.47619 656.446886,814.534799 692.564103,814.534799 C742.380952,814.534799 780.3663,785.89011 780.3663,744.168498 L780.3663,744.168498 L780.3663,721.750916 C761.062271,714.278388 735.531136,708.673993 705.641026,708.673993 Z M919.461538,414.666667 C941.552928,414.666667 959.461538,432.575277 959.461538,454.666667 C959.461538,476.758057 941.552928,494.666667 919.461538,494.666667 L487.461538,494.666667 C465.370148,494.666667 447.461538,476.758057 447.461538,454.666667 C447.461538,432.575277 465.370148,414.666667 487.461538,414.666667 L919.461538,414.666667 Z M707.509158,40 C756.703297,40 793.443223,53.0769231 816.483516,76.7399267 C840.769231,100.40293 851.978022,135.274725 851.978022,178.241758 L851.978022,178.241758 L851.978022,340.14652 C851.978022,360.695971 835.787546,376.263736 815.238095,376.263736 C793.443223,376.263736 778.498168,361.318681 778.498168,344.505495 L778.498168,344.505495 L778.498168,332.051282 C756.080586,358.827839 721.831502,380 671.391941,380 C609.74359,380 554.945055,344.505495 554.945055,278.498168 L554.945055,278.498168 L554.945055,277.252747 C554.945055,206.263736 610.3663,171.391941 690.695971,171.391941 C727.435897,171.391941 753.589744,176.996337 779.120879,185.091575 L779.120879,185.091575 L779.120879,176.996337 C779.120879,130.29304 750.47619,105.384615 697.545788,105.384615 C668.901099,105.384615 645.238095,110.3663 624.688645,118.461538 C620.32967,119.70696 616.593407,120.32967 612.857143,120.32967 C595.421245,120.32967 581.098901,106.630037 581.098901,89.1941392 C581.098901,75.4945055 590.43956,63.6630037 601.648352,59.3040293 C632.783883,47.4725275 664.542125,40 707.509158,40 Z M705.641026,219.340659 C657.069597,219.340659 628.424908,239.89011 628.424908,274.139194 L628.424908,274.139194 L628.424908,275.384615 C628.424908,307.142857 656.446886,325.201465 692.564103,325.201465 C742.380952,325.201465 780.3663,296.556777 780.3663,254.835165 L780.3663,254.835165 L780.3663,232.417582 C761.062271,224.945055 735.531136,219.340659 705.641026,219.340659 Z"></path>
)

export const TextDecoration = (
  <path d="M551.208577,113.100229 C571.408577,121.400229 587.508577,137.900229 595.408577,158.200229 L734.856,519 L863,519 C890.061953,519 912,540.938047 912,568 C912,595.061953 890.061953,617 863,617 L772.732,617 L867.308577,861.700229 C875.308577,882.300229 865.008577,905.500229 844.408577,913.400229 C839.708577,915.200229 834.808577,916.100229 830.008577,916.100229 C814.008577,916.100229 798.808577,906.400229 792.708577,890.500229 L687.001,617 L343.875,617 L231.008577,891.300229 C222.608577,911.700229 199.208577,921.500229 178.808577,913.100229 C158.408577,904.700229 148.608577,881.300229 157.008577,860.900229 L257.366,617 L161,617 C133.938047,617 112,595.061953 112,568 C112,540.938047 133.938047,519 161,519 L297.69,519 L446.808577,156.600229 C454.908577,136.800229 470.308577,121.400229 490.008577,113.200229 C509.708577,105.000229 531.508577,104.900229 551.208577,113.100229 Z M520.808577,187.000229 L384.199,519 L649.125,519 L520.808577,187.000229 Z"></path>
)

export const TextUnderline = (
  <path d="M772,844 C794.09139,844 812,861.90861 812,884 C812,906.09139 794.09139,924 772,924 L252,924 C229.90861,924 212,906.09139 212,884 C212,861.90861 229.90861,844 252,844 L772,844 Z M272,100 C274.155215,100 276.27062,100.17045 278.333468,100.498605 L752,100.5 L752,100.5 C774.09139,100.5 792,118.40861 792,140.5 L792,190.5 C792,212.59139 774.09139,230.5 752,230.5 C729.90861,230.5 712,212.59139 712,190.5 L712,180.5 L552,180.5 L552,663.5 L672,663.5 C694.09139,663.5 712,681.40861 712,703.5 C712,725.59139 694.09139,743.5 672,743.5 L352,743.5 C329.90861,743.5 312,725.59139 312,703.5 C312,681.40861 329.90861,663.5 352,663.5 L472,663.5 L472,180.499 L312,180.499 L312,190 C312,212.09139 294.09139,230 272,230 C249.90861,230 232,212.09139 232,190 L232,140 C232,117.90861 249.90861,100 272,100 Z"></path>
)

export const TextLineThrough = (
  <path d="M551.208577,113.100229 C571.408577,121.400229 587.508577,137.900229 595.408577,158.200229 L734.856,519 L863,519 C890.061953,519 912,540.938047 912,568 C912,595.061953 890.061953,617 863,617 L772.732,617 L867.308577,861.700229 C875.308577,882.300229 865.008577,905.500229 844.408577,913.400229 C839.708577,915.200229 834.808577,916.100229 830.008577,916.100229 C814.008577,916.100229 798.808577,906.400229 792.708577,890.500229 L687.001,617 L343.875,617 L231.008577,891.300229 C222.608577,911.700229 199.208577,921.500229 178.808577,913.100229 C158.408577,904.700229 148.608577,881.300229 157.008577,860.900229 L257.366,617 L161,617 C133.938047,617 112,595.061953 112,568 C112,540.938047 133.938047,519 161,519 L297.69,519 L446.808577,156.600229 C454.908577,136.800229 470.308577,121.400229 490.008577,113.200229 C509.708577,105.000229 531.508577,104.900229 551.208577,113.100229 Z M520.808577,187.000229 L384.199,519 L649.125,519 L520.808577,187.000229 Z"></path>
)

export const TextAlign = (
  <path d="M461.421356,629.715729 C477.042328,645.3367 477.042328,670.6633 461.421356,686.284271 L416.567,731.137 L884,731.137085 C906.09139,731.137085 924,749.045695 924,771.137085 C924,793.228475 906.09139,811.137085 884,811.137085 L416.568,811.137 L461.421356,855.989899 C477.042328,871.61087 477.042328,896.93747 461.421356,912.558441 C445.800385,928.179413 420.473785,928.179413 404.852814,912.558441 L291.715729,799.421356 C276.094757,783.800385 276.094757,758.473785 291.715729,742.852814 L404.852814,629.715729 C420.473785,614.094757 445.800385,614.094757 461.421356,629.715729 Z M140,100 C162.09139,100 180,117.90861 180,140 L180,884 C180,906.09139 162.09139,924 140,924 C117.90861,924 100,906.09139 100,884 L100,140 C100,117.90861 117.90861,100 140,100 Z M884,478 C906.09139,478 924,495.90861 924,518 C924,540.09139 906.09139,558 884,558 L320,558 C297.90861,558 280,540.09139 280,518 C280,495.90861 297.90861,478 320,478 L884,478 Z M884,289 C906.09139,289 924,306.90861 924,329 C924,351.09139 906.09139,369 884,369 L320,369 C297.90861,369 280,351.09139 280,329 C280,306.90861 297.90861,289 320,289 L884,289 Z M884,100 C906.09139,100 924,117.90861 924,140 C924,162.09139 906.09139,180 884,180 L320,180 C297.90861,180 280,162.09139 280,140 C280,117.90861 297.90861,100 320,100 L884,100 Z"></path>
)

export const TextAlignLeft = (
  <path d="M884,844 C906.09139,844 924,861.90861 924,884 C924,906.09139 906.09139,924 884,924 L140,924 C117.90861,924 100,906.09139 100,884 C100,861.90861 117.90861,844 140,844 L884,844 Z M590,658 C612.09139,658 630,675.90861 630,698 C630,720.09139 612.09139,738 590,738 L140,738 C117.90861,738 100,720.09139 100,698 C100,675.90861 117.90861,658 140,658 L590,658 Z M884,472 C906.09139,472 924,489.90861 924,512 C924,534.09139 906.09139,552 884,552 L140,552 C117.90861,552 100,534.09139 100,512 C100,489.90861 117.90861,472 140,472 L884,472 Z M590,286 C612.09139,286 630,303.90861 630,326 C630,348.09139 612.09139,366 590,366 L140,366 C117.90861,366 100,348.09139 100,326 C100,303.90861 117.90861,286 140,286 L590,286 Z M884,100 C906.09139,100 924,117.90861 924,140 C924,162.09139 906.09139,180 884,180 L140,180 C117.90861,180 100,162.09139 100,140 C100,117.90861 117.90861,100 140,100 L884,100 Z"></path>
)

export const TextAlignCenter = (
  <path d="M884,844 C906.09139,844 924,861.90861 924,884 C924,906.09139 906.09139,924 884,924 L140,924 C117.90861,924 100,906.09139 100,884 C100,861.90861 117.90861,844 140,844 L884,844 Z M737,658 C759.09139,658 777,675.90861 777,698 C777,720.09139 759.09139,738 737,738 L287,738 C264.90861,738 247,720.09139 247,698 C247,675.90861 264.90861,658 287,658 L737,658 Z M884,472 C906.09139,472 924,489.90861 924,512 C924,534.09139 906.09139,552 884,552 L140,552 C117.90861,552 100,534.09139 100,512 C100,489.90861 117.90861,472 140,472 L884,472 Z M737,286 C759.09139,286 777,303.90861 777,326 C777,348.09139 759.09139,366 737,366 L287,366 C264.90861,366 247,348.09139 247,326 C247,303.90861 264.90861,286 287,286 L737,286 Z M884,100 C906.09139,100 924,117.90861 924,140 C924,162.09139 906.09139,180 884,180 L140,180 C117.90861,180 100,162.09139 100,140 C100,117.90861 117.90861,100 140,100 L884,100 Z"></path>
)

export const TextAlignRight = (
  <path d="M884,844 C906.09139,844 924,861.90861 924,884 C924,906.09139 906.09139,924 884,924 L140,924 C117.90861,924 100,906.09139 100,884 C100,861.90861 117.90861,844 140,844 L884,844 Z M884,658 C906.09139,658 924,675.90861 924,698 C924,720.09139 906.09139,738 884,738 L434,738 C411.90861,738 394,720.09139 394,698 C394,675.90861 411.90861,658 434,658 L884,658 Z M884,472 C906.09139,472 924,489.90861 924,512 C924,534.09139 906.09139,552 884,552 L140,552 C117.90861,552 100,534.09139 100,512 C100,489.90861 117.90861,472 140,472 L884,472 Z M884,286 C906.09139,286 924,303.90861 924,326 C924,348.09139 906.09139,366 884,366 L434,366 C411.90861,366 394,348.09139 394,326 C394,303.90861 411.90861,286 434,286 L884,286 Z M884,100 C906.09139,100 924,117.90861 924,140 C924,162.09139 906.09139,180 884,180 L140,180 C117.90861,180 100,162.09139 100,140 C100,117.90861 117.90861,100 140,100 L884,100 Z"></path>
)

export const TextAlignJustify = (
  <path d="M590,844 C612.09139,844 630,861.90861 630,884 C630,906.09139 612.09139,924 590,924 L140,924 C117.90861,924 100,906.09139 100,884 C100,861.90861 117.90861,844 140,844 L590,844 Z M884,658 C906.09139,658 924,675.90861 924,698 C924,720.09139 906.09139,738 884,738 L140,738 C117.90861,738 100,720.09139 100,698 C100,675.90861 117.90861,658 140,658 L884,658 Z M884,472 C906.09139,472 924,489.90861 924,512 C924,534.09139 906.09139,552 884,552 L140,552 C117.90861,552 100,534.09139 100,512 C100,489.90861 117.90861,472 140,472 L884,472 Z M884,286 C906.09139,286 924,303.90861 924,326 C924,348.09139 906.09139,366 884,366 L140,366 C117.90861,366 100,348.09139 100,326 C100,303.90861 117.90861,286 140,286 L884,286 Z M884,100 C906.09139,100 924,117.90861 924,140 C924,162.09139 906.09139,180 884,180 L140,180 C117.90861,180 100,162.09139 100,140 C100,117.90861 117.90861,100 140,100 L884,100 Z"></path>
)

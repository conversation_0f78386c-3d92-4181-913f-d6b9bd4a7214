export default {
  'zh-CN': {
    operations: {
      default_state: '缺省态',
      append_node: '追加节点',
      prepend_node: '头部追加',
      clone_node: '复制节点',
      update_node_props: '属性更改',
      insert_after: '后置插入',
      insert_before: '前置插入',
      insert_children: '插入子节点',
      update_children: '覆盖子节点',
      remove_node: '删除节点',
      wrap_node: '包装节点',
      from_node: '子树更新',
    },
  },
  'en-US': {
    operations: {
      default_state: 'Default State',
      append_node: 'Append Node',
      prepend_node: 'Prepend Node',
      clone_node: 'Clone Node',
      update_node_props: 'Update Node Props',
      insert_after: 'Insert Node After',
      insert_before: 'Insert Node Before',
      insert_children: 'Insert Node Children',
      update_children: 'Update Children',
      remove_node: 'Remove Node',
      wrap_node: 'Wrap Node',
      from_node: 'Update Child Tree',
    },
  },
}

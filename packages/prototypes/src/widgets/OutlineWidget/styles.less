@import '../../variables.less';

.@{prefix-cls}-outline-tree {
  &-container {
    position: relative;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: var(--dn-outline-tree-bg-color);
  }

  &-header {
    display: flex;
    padding: 8px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--dn-outline-tree-header-border-color);
    color: var(--dn-outline-tree-color);
  }

  &-title {
    font-size: 16px;
    font-weight: 500;
  }

  &-close {
    display: flex;
    align-items: center;
    transform: scale(1.6);
    cursor: pointer;
  }

  &-content {
    position: relative;
    flex-grow: 1;
    height: 100%;
    width: 100%;
    padding-bottom: 20px;
    overflow: overlay;
  }

  &-aux {
    position: absolute;
    top: 0;
    left: 0;
  }

  &-insertion {
    background-color: var(--dn-outline-tree-insertion-bg-color);
  }

  &-node {
    position: relative;
    user-select: none;
    width: fit-content;
    min-width: 100%;

    &.expanded {
      & > .@{prefix-cls}-outline-tree-node-header {
        .@{prefix-cls}-outline-tree-node-expand {
          transform: rotate(0);
        }
      }

      & > .@{prefix-cls}-outline-tree-node-children {
        display: block;
      }
    }

    &.selected {
      & > .@{prefix-cls}-outline-tree-node-header {
        background-color: var(--dn-panel-active-bg-color);

        .@{prefix-cls}-outline-tree-node-header-head {
          background-color: var(--dn-panel-active-bg-color);
        }
      }
    }

    &.droppable {
      & > .@{prefix-cls}-outline-tree-node-header {
        .@{prefix-cls}-outline-tree-node-header-content {
          .@{prefix-cls}-outline-tree-node-header-base {
            & > .@{prefix-cls}-outline-tree-node-icon {
              transform: scale(1.2);
            }
          }
        }
      }
    }

    &-hidden-icon:not(.hidden) {
      display: none;
    }

    &-header {
      display: flex;
      min-height: 32px;
      width: fit-content;
      min-width: 100%;
      align-items: center;
      color: var(--dn-outline-tree-node-header-color);
      position: relative;
      padding-left: 8px;

      &:hover {
        .@{prefix-cls}-outline-tree-node-header-content {
          color: var(--dn-outline-tree-node-hover-color);
        }

        .@{prefix-cls}-outline-tree-node-hidden-icon {
          display: block;
        }
      }
    }

    &-header-head {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      pointer-events: none;
    }

    &-header-content {
      display: flex;
      align-items: center;
      transition: all 0.15s ease-in;
      transform-origin: left;
      width: fit-content;
      min-width: 100%;
      height: 100%;
      justify-content: space-between;
      font-size: 12px;
    }

    &-header-base {
      display: flex;
      align-items: center;
    }

    &-header-actions {
      display: flex;
      align-items: center;
      margin-right: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &-expand {
      display: flex;
      align-items: center;
      transition: all 0.15s ease-out;
      transform: rotate(-90deg);
      margin-right: 3px;
      width: 12px;
    }

    &-icon {
      margin-right: 5px;
      display: flex;
      align-items: center;
      font-size: 12px;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    &-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 100px;
    }

    &-actions {
      font-size: 12px;
      flex-grow: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &-children {
      padding-left: 16px;
      display: none;
      width: fit-content;
      min-width: 100%;
    }
  }
}

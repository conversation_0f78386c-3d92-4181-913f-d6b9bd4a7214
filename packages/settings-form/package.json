{"name": "@formily/element-plus-settings-form", "version": "1.0.0-beta.5", "license": "MIT", "main": "lib", "types": "lib/index.d.ts", "engines": {"npm": ">=3.0.0"}, "module": "esm", "umd:main": "dist/formily.element-plus-settings-form.umd.production.js", "unpkg": "dist/formily.element-plus-settings-form.umd.production.js", "jsdelivr": "dist/formily.element-plus-settings-form.umd.production.js", "jsnext:main": "esm", "sideEffects": ["dist/*", "esm/*.js", "lib/*.js", "src/*.ts", "*.less", "*.scss", "**/*/style.js"], "scripts": {"build": "formily-tpl build"}, "repository": {"type": "git", "url": "git+https://github.com/formilyjs/element-plus.git"}, "bugs": {"url": "https://github.com/formilyjs/element-plus/issues"}, "homepage": "https://github.com/formilyjs/element-plus#readme", "publishConfig": {"access": "public"}, "peerDependencies": {"vue": "^3.2.24"}, "dependencies": {"@formily/core": "^2.0.20", "@formily/grid": "^2.0.20", "@formily/json-schema": "^2.0.20", "@formily/reactive": "^2.0.20", "@formily/reactive-vue": "^2.0.20", "@formily/shared": "^2.0.20", "@formily/vue": "^2.0.20", "vue-slicksort": "^1.2.0"}, "devDependencies": {"vue": "^3.2.24"}}
@formily-element-prefix: ~'formily-element-plus';
@element-prefix: ~'el';

@text-color: rgba(0, 0, 0, 0.85);
@border-color-split: #f0f0f0;
@border-color-base: #d9d9d9;

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(100%);
  }
}

.animate__slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight;
}

.animate__slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight;
}

.animate__animated {
  animation-delay: 0ms;
  animation-duration: 0.25s;
  animation-fill-mode: forwards;
}

.animate__fadeInUp {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

.dn-settings-form-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  .dn-node-path {
    flex-grow: 0;
  }

  .dn-settings-form-content {
    flex-grow: 1;
    overflow: overlay;
  }

  .formily-element-plus-form-item-label-content {
    overflow: visible;
  }

  .@{formily-element-prefix}-form-item {
    border-bottom: 1px solid @border-color-split;
    padding-bottom: 8px;
    margin-bottom: 8px;
    margin-top: 8px;

    * {
      font-size: 13px;
    }

    .el-radio-button__inner {
      padding: 8px !important;
    }

    .@{formily-element-prefix}-form-item-control-content-component {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      & > .@{element-prefix}-radio-group {
        display: flex !important;
        height: 100%;

        .@{element-prefix}-radio-button {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
        }

        .@{element-prefix}-radio-button__inner {
          flex-grow: 2;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          padding: 0 6px !important;
        }
      }

      & > .@{element-prefix}-slider {
        flex-shrink: 0;
        min-width: 0;
        width: 100%;
      }
    }
  }
}

.dn-settings-form {
  padding: 0 20px;

  &-empty {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    color: #888;
  }
}

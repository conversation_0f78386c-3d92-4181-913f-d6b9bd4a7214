@import '../../styles.less';

.dn-input-items {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8px;

  &-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: @text-color;

    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 8px;
      flex-shrink: 0;
      flex-grow: 0;
      color: @text-color;
    }

    &-controller {
      min-width: 0;
      flex-shrink: 1;
      flex-grow: 1;
      overflow: hidden;

      .@{element-prefix}-input-number {
        width: 100%;

        .@{element-prefix}-input {
          &__wrapper {
            padding: 0 !important;
          }
        }
      }

      .@{element-prefix}-radio-group {
        display: flex;

        .@{element-prefix}-radio-button {
          display: flex;
          flex: 1;
          justify-content: center;
          align-items: center;
          height: 100%;
        }

        .@{element-prefix}-radio-button__inner {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 8px !important;
        }
      }
    }

    &.vertical {
      flex-direction: column;
      align-items: flex-start;

      .dn-input-items-item-controller {
        width: 100%;
      }
    }
  }
}

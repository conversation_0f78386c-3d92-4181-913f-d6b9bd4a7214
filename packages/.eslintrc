{
  "parser": "vue-eslint-parser",
  "extends": [
    "plugin:vue/vue3-recommended",
    "plugin:@typescript-eslint/recommended",
    "prettier/@typescript-eslint"
  ],
  "env": {
    "node": true
  },
  "plugins": ["@typescript-eslint", "prettier", "markdown"],
  "parserOptions": {
    "sourceType": "module",
    "parser": "@typescript-eslint/parser",
    "ecmaVersion": 10,
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "rules": {
    "prettier/prettier": 0,
    // don't force es6 functions to include space before paren
    "space-before-function-paren": 0,
    // maybe we should no-public
    "@typescript-eslint/explicit-member-accessibility": 0,
    "@typescript-eslint/interface-name-prefix": 0,
    "@typescript-eslint/no-explicit-any": 0,
    "@typescript-eslint/explicit-function-return-type": 0,
    "@typescript-eslint/no-parameter-properties": 0,
    "@typescript-eslint/array-type": 0,
    "@typescript-eslint/no-object-literal-type-assertion": 0,
    "@typescript-eslint/no-use-before-define": 0,
    "@typescript-eslint/no-unused-vars": 1,
    "@typescript-eslint/no-namespace": 0,
    "@typescript-eslint/ban-ts-comment": 0,
    "@typescript-eslint/ban-types": 0,
    "@typescript-eslint/adjacent-overload-signatures": 0,
    "@typescript-eslint/explicit-module-boundary-types": 0,
    "@typescript-eslint/triple-slash-reference": 0,
    "@typescript-eslint/no-empty-function": 0,
    "vue/require-prop-types": 0,
    "no-console": [
      "error",
      {
        "allow": ["warn", "error", "info"]
      }
    ],
    "prefer-const": 0,
    "no-var": 1,
    "prefer-rest-params": 0,
    "vue/one-component-per-file": 0,
    "vue/require-default-prop": 0,
    "vue/max-attributes-per-line": 0
  },
  "overrides": [
    {
      "files": ["**/*.md.{jsx,tsx}"],
      "processor": "markdown/markdown"
    },
    {
      "files": ["**/*.md/*.{jsx,tsx}"],
      "rules": {
        "@typescript-eslint/no-unused-vars": "error",
        "no-unused-vars": "error",
        "no-console": "off"
      }
    },
    {
      "files": ["**/*.md/*.{js,ts}"],
      "rules": {
        "@typescript-eslint/no-unused-vars": "off",
        "no-unused-vars": "off",
        "no-console": "off"
      }
    },
    {
      "files": ["**/*/docs/**/*.{js,ts,tsx,vue}"],
      "rules": {
        "no-console": "off"
      }
    }
  ]
}

{"name": "@formily/element-plus-setters", "version": "1.0.0-beta.5", "license": "MIT", "main": "lib", "types": "lib/index.d.ts", "engines": {"npm": ">=3.0.0"}, "module": "esm", "umd:main": "dist/formily.element-plus-setters.umd.production.js", "unpkg": "dist/formily.element-plus-setters.umd.production.js", "jsdelivr": "dist/formily.element-plus-setters.umd.production.js", "jsnext:main": "esm", "sideEffects": ["dist/*", "esm/*.js", "lib/*.js", "src/*.ts", "*.less", "*.scss", "**/*/style.js"], "scripts": {"build": "formily-tpl build"}, "repository": {"type": "git", "url": "git+https://github.com/formilyjs/element-plus.git"}, "bugs": {"url": "https://github.com/formilyjs/element-plus/issues"}, "homepage": "https://github.com/formilyjs/element-plus#readme", "publishConfig": {"access": "public"}, "peerDependencies": {"vue": "^3.2.24"}}
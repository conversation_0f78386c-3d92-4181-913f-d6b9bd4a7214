export const Input = {
  'zh-CN': {
    title: '输入框',
    settings: {
      'x-component-props': {
        maxlength: '最大输入长度',
        minlength: '最小输入长度',
        'show-word-limit': '显示输入字数',
        placeholder: '占位提示',
        clearable: '可清空',
        'prefix-icon': '输入框头部图标',
        'suffix-icon': '输入框尾部图标',
        autofocus: '获取焦点',
        size: '尺寸',
        rows: '行数',
        autoSize: {
          title: '自适应高度',
          tooltip: '可设置为 true | false 或对象：{ minRows: 2, maxRows: 6 }',
        },
      },
    },
  },
  'en-US': {
    title: 'Input',
    settings: {
      'x-component-props': {
        maxlength: '最大输入长度',
        minlength: '最小输入长度',
        'show-word-limit': '显示输入字数',
        placeholder: '占位提示',
        clearable: '可清空',
        'prefix-icon': '输入框头部图标',
        'suffix-icon': '输入框尾部图标',
        autofocus: '获取焦点',
        size: '尺寸',
        autoSize: {
          title: '自适应高度',
          tooltip: '可设置为 true | false 或对象：{ minRows: 2, maxRows: 6 }',
        },
      },
    },
  },
}

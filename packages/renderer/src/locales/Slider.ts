export const Slider = {
  'zh-CN': {
    title: '滑动条',
    settings: {
      'x-component-props': {
        dots: '刻度固定',
        range: '双滑块',
        reverse: '反向坐标系',
        vertical: '垂直布局',
        tooltipPlacement: {
          title: '提示位置',
          tooltip: '设置 提示 展示位置。参考 Tooltip',
        },
        tooltipVisible: {
          title: '提示显示',
          tooltip:
            '开启时，提示 将会始终显示；否则始终不显示，哪怕在拖拽及移入时',
        },
        max: '最大值',
        min: '最小值',
        step: '步长',
        marks: '刻度标签',
      },
    },
  },
  'en-US': {
    title: 'Slider',
    settings: {
      'x-component-props': {
        dots: 'Fixed Scale',
        range: 'Double Slider',
        reverse: 'Reverse Coordinate System',
        vertical: 'Vertical',
        tooltipPlacement: {
          title: 'Tooltip Placement',
          tooltip: 'Set up prompt placement. Reference Tooltip',
        },
        tooltipVisible: {
          title: 'Tooltip Visible',
          tooltip:
            'When turned on, the prompt will always be displayed; otherwise, it will always not be displayed, even when dragging and moving in',
        },
        max: 'Max',
        min: 'Min',
        step: 'Step',
        marks: 'Marks',
      },
    },
  },
}

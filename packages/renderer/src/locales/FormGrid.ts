export const FormGrid = {
  'zh-CN': {
    title: '网格布局',
    addGridColumn: '添加网格列',
    settings: {
      'x-component-props': {
        minWidth: '最小宽度',
        minColumns: '最小列数',
        maxWidth: '最大宽度',
        maxColumns: '最大列数',
        breakpoints: '响应式断点',
        columnGap: '列间距',
        rowGap: '行间距',
        colWrap: '自动换行',
        strictAutoFit:
          'GridItem 宽度是否严格受限于 maxWidth，不受限的话会自动占满容器',
      },
    },
  },
  'en-US': {
    title: 'Grid',
    addGridColumn: 'Add Grid Column',
    settings: {
      'x-component-props': {
        minWidth: 'Min Width',
        minColumns: 'Min Columns',
        maxWidth: 'Max Width',
        maxColumns: 'Max Columns',
        breakpoints: 'Breakpoints',
        columnGap: 'Column Gap',
        rowGap: 'Row Gap',
        colWrap: 'Col Wrap',
        strictAutoFit:
          'GridItem 宽度是否严格受限于 maxWidth，不受限的话会自动占满容器',
      },
    },
  },
}

export const FormGridColumn = {
  'zh-CN': {
    title: '网格列',
    settings: {
      'x-component-props': {
        gridSpan: '跨列栏数',
      },
    },
  },
  'en-US': {
    title: 'Grid Column',
    settings: {
      'x-component-props': {
        gridSpan: 'Grid Span',
      },
    },
  },
}

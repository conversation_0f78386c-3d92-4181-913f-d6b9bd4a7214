{"name": "@formily/element-plus-renderer", "version": "1.0.0-beta.5", "license": "MIT", "main": "lib", "types": "lib/index.d.ts", "engines": {"npm": ">=3.0.0"}, "module": "esm", "umd:main": "dist/formily.element-plus-renderer.umd.production.js", "unpkg": "dist/formily.element-plus-renderer.umd.production.js", "jsdelivr": "dist/formily.element-plus-renderer.umd.production.js", "jsnext:main": "esm", "sideEffects": ["dist/*", "esm/*.js", "lib/*.js", "src/*.ts", "*.less", "*.scss", "**/*/style.js"], "scripts": {"build": "formily-tpl build"}, "repository": {"type": "git", "url": "git+https://github.com/formilyjs/element-plus.git"}, "bugs": {"url": "https://github.com/formilyjs/element-plus/issues"}, "homepage": "https://github.com/formilyjs/element-plus#readme", "publishConfig": {"access": "public"}, "peerDependencies": {"vue": "^3.2.24"}, "dependencies": {}, "devDependencies": {"@formily/element-plus-setters": "1.0.0-beta.2"}}
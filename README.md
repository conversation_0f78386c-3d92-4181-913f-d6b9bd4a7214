## 基于 designable v1.0.0-beta.25 实现

- 兼容到 designable v1.0.0-beta.45(中间没准有漏掉的 commit)

English | [简体中文](README.zh-CN.md)

<p align="center">
<img width="600" src="https://img.alicdn.com/imgextra/i1/O1CN01bg1tTN1p5ZOPmhKV0_!!6000000005309-55-tps-2200-981.svg">
</p>

<p align="center">
<img src="https://img.alicdn.com/tfs/TB1fHhZu4D1gK0jSZFyXXciOVXa-2500-1200.png">
<img src="https://img.shields.io/npm/dt/@formily/element-plus"/>
<img src="https://img.shields.io/npm/dm/@formily/element-plus"/>
<a href="https://www.npmjs.com/package/@formily/element-plus"><img src="https://img.shields.io/npm/v/@formily/element-plus.svg"></a>
<a href="https://codecov.io/gh/formilyjs/element-plus">
  <img src="https://codecov.io/gh/formilyjs/element-plus/branch/master/graph/badge.svg?token=3V9RU8Wh9d"/>
</a>
<img alt="PRs Welcome" src="https://img.shields.io/badge/PRs-welcome-brightgreen.svg"/>
<a href="https://github.com/actions-cool/issues-helper">
  <img src="https://img.shields.io/badge/using-issues--helper-blueviolet"/>
</a>
</p>

---

## Overview

The Awesome Components Library with Formily & Element Plus.

## Features

- 🖼 Designable, You can quickly develop forms at low cost through [Form Builder](https://qq1037305420.github.io/element-plus/).
- 🚀 High performance, fields managed independently, rather rerender the whole tree.
- 💡 Integrated Alibaba Fusion and Ant Design components are guaranteed to work out of the box.
- 🎨 JSON Schema applied for BackEnd. JSchema applied for FrontEnd. Two paradigms can be converted to each other.
- 🏅 Side effects are managed independently, making form data linkages easier than ever before.
- 🌯 Override most complicated form layout use cases.

## Form Builder

![image](https://github.com/user-attachments/assets/8ed0c971-7ad3-418d-a3c1-28edd75ce276)


## WebSite

https://designable-vue.vercel.app/

## Community

- [formily](https://github.com/alibaba/formily)
- [formilyjs](https://github.com/formilyjs)
- [designable](https://github.com/alibaba/designable)

## How to contribute?

- [Contribute document](https://formilyjs.org/zh-CN/guide/contribution)

## Contributors

This project exists thanks to all the people who contribute.

<a href="https://github.com/formilyjs/designable-vue/graphs/contributors"><img src="https://contrib.rocks/image?repo=formilyjs/designable-vue" /></a>

## LICENSE

Formily is open source software licensed as
[MIT](LICENSE.md).

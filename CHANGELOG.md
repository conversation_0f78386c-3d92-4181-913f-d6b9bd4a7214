# Changelog

## v1.0.0-beta.5(2022-07-05)

### :bug: Bug Fixes

1. [fix: fix form-item labelWidth and wrapperWidth error](https://github.com/formilyjs/element-plus/commit/6e74041) :point_right: ( [yi<PERSON><PERSON>](https://github.com/yiyunwan) )

## v1.0.0-beta.4(2022-07-05)

### :bug: Bug Fixes

1. [fix: fix eslint warning](https://github.com/formilyjs/element-plus/commit/f34b0ea) :point_right: ( [yiyunwan](https://github.com/yiyunwan) )

1. [fix: remove object key](https://github.com/formilyjs/element-plus/commit/f305afb) :point_right: ( [wanghansong](https://github.com/wanghansong) )

1. [fix: form component inherit attrs issue](https://github.com/formilyjs/element-plus/commit/4cb72da) :point_right: ( [qq1037305420](https://github.com/qq1037305420) )

1. [bugfix: fix options data change but not rerender](https://github.com/formilyjs/element-plus/commit/6d5eb5b) :point_right: ( [wanghansong](https://github.com/wanghansong) )

### :memo: Documents Changes

1. [docs: fix docs table error](https://github.com/formilyjs/element-plus/commit/2ee721f) :point_right: ( [yiyunwan](https://github.com/yiyunwan) )

## v1.0.0-beta.3(2022-07-04)

### :bug: Bug Fixes

1. [fix: change form-item style file](https://github.com/formilyjs/element-plus/commit/13b95db) :point_right: ( [yiyunwan](https://github.com/yiyunwan) )

1. [fix: create codesandbox script](https://github.com/formilyjs/element-plus/commit/d761fc4) :point_right: ( [qq1037305420](https://github.com/qq1037305420) )

### :memo: Documents Changes

1. [docs: 修改按需引入文档](https://github.com/formilyjs/element-plus/commit/1486198) :point_right: ( [yiyunwan](https://github.com/yiyunwan) )

1. [docs: change Element Plus url](https://github.com/formilyjs/element-plus/commit/e61d56e) :point_right: ( [yiyunwan](https://github.com/yiyunwan) )

## v1.0.0-beta.2(2022-06-09)

### :tada: Enhancements

1. [feat: del test](https://github.com/formilyjs/element-plus/commit/be28b71) :point_right: ( [yiyunwan](https://github.com/yiyunwan) )

1. [feat: add jest-dom](https://github.com/formilyjs/element-plus/commit/011d9e3) :point_right: ( [yiyunwan](https://github.com/yiyunwan) )

1. [feat: 完善文档](https://github.com/formilyjs/element-plus/commit/a1e373b) :point_right: ( [wukd](https://github.com/wukd) )

1. [feat: add docs of base components and array-\* components](https://github.com/formilyjs/element-plus/commit/f54676b) :point_right: ( [wukd](https://github.com/wukd) )

1. [feat: update form\* components](https://github.com/formilyjs/element-plus/commit/b3b2947) :point_right: ( [赵伟(004691)](<https://github.com/赵伟(004691)>) )

### :bug: Bug Fixes

1. [fix: add @formily/template deps](https://github.com/formilyjs/element-plus/commit/9c1d91d) :point_right: ( [wukd](https://github.com/wukd) )

1. [bugfix: fix checkbox-group v-model not effect](https://github.com/formilyjs/element-plus/commit/a1e1405) :point_right: ( [wanghansong](https://github.com/wanghansong) )

1. [fix: docs](https://github.com/formilyjs/element-plus/commit/1a49074) :point_right: ( [wukd](https://github.com/wukd) )

### :memo: Documents Changes

1. [docs: move docs to root dir](https://github.com/formilyjs/element-plus/commit/ec17516) :point_right: ( [wukd](https://github.com/wukd) )

### :blush: Other Changes

1. [chore: first commit](https://github.com/formilyjs/element-plus/commit/dd22c2b) :point_right: ( [zhaowei-plus](https://github.com/zhaowei-plus) )

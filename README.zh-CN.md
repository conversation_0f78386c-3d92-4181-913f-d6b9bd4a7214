[English](README.md) | 简体中文

<p align="center">
<img src="https://img.alicdn.com/tfs/TB1fHhZu4D1gK0jSZFyXXciOVXa-2500-1200.png">
<img src="https://img.shields.io/npm/dt/@formily/element-plus"/>
<img src="https://img.shields.io/npm/dm/@formily/element-plus"/>
<a href="https://www.npmjs.com/package/@formily/element-plus"><img src="https://img.shields.io/npm/v/@formily/element-plus.svg"></a>
<a href="https://codecov.io/gh/formilyjs/element-plus">
  <img src="https://codecov.io/gh/formilyjs/element-plus/branch/master/graph/badge.svg?token=3V9RU8Wh9d"/>
</a>
<img alt="PRs Welcome" src="https://img.shields.io/badge/PRs-welcome-brightgreen.svg"/>
<a href="https://github.com/actions-cool/issues-helper">
  <img src="https://img.shields.io/badge/using-issues--helper-blueviolet"/>
</a>
</p>

---

## 概要

这是一个结合了 Formily & Element Plus 的超酷组件库.

## 特性

- 🖼 可设计，借助 Form Builder 可以快速搭建表单
- 🚀 高性能，字段分布式渲染，大大减轻 React 渲染压力
- 💡 支持 Ant Design/Fusion Next 组件体系
- 🎨 JSX 标签化写法/JSON Schema 数据驱动方案无缝迁移过渡
- 🏅 副作用逻辑独立管理，涵盖各种复杂联动校验逻辑
- 🌯 支持各种表单复杂布局方案

## [表单设计器](https://qq1037305420.github.io/element-plus/)

![https://qq1037305420.github.io/element-plus/](https://s3.bmp.ovh/imgs/2022/06/13/c632f4feee0e21a2.png)

## 官网

https://element-plus.formilyjs.org

## 生态产品

- [formily](https://github.com/alibaba/formily)
- [formilyjs](https://github.com/formilyjs)
- [designable](https://github.com/alibaba/designable)

## 如何贡献?

- [Contribute document](https://formilyjs.org/zh-CN/guide/contribution)

## 贡献者

This project exists thanks to all the people who contribute.
<a href="https://github.com/formilyjs/element-plus/graphs/contributors"><img src="https://contrib.rocks/image?repo=formilyjs/element-plus" /></a>

## LICENSE

Formily is open source software licensed as
[MIT](LICENSE.md).

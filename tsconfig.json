{"compilerOptions": {"esModuleInterop": true, "moduleResolution": "node", "jsx": "preserve", "module": "commonjs", "target": "es5", "allowJs": false, "noUnusedLocals": false, "preserveConstEnums": true, "skipLibCheck": true, "sourceMap": true, "inlineSources": true, "declaration": true, "experimentalDecorators": true, "downlevelIteration": true, "baseUrl": "./.idea", "paths": {"@formily/element-plus": ["packages/components/src"], "@formily/element-plus-*": ["packages/*/src"]}, "types": ["vite/client"]}}
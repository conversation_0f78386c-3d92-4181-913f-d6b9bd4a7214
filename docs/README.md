---
home: true
heroText: Formily Element Plus
tagline: 基于 element-plus 封装的Formily2.x组件体系
actions:
  - text: 组件文档
    link: /guide/
features:
  - title: 更易用
    details: 开箱即用，案例丰富
  - title: 更高效
    details: 傻瓜写法，超高性能
  - title: 更专业
    details: 完备，灵活，优雅
footer: Open-source MIT Licensed | Copyright © 2022-present
---

## 安装

vue3:

```bash
$ npm install --save element-plus
$ npm install --save @formily/core @formily/vue @formily/element-plus
```

## 快速开始

<dumi-previewer demoPath="index" :collapsed="false" />

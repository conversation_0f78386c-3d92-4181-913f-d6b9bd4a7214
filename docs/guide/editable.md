# Editable

> 局部编辑器，对于一些空间要求较高的表单区域可以使用该组件
>
> Editable 组件相当于是 FormItem 组件的变体，所以通常放在 decorator 中

## Markup Schema 案例

<dumi-previewer demoPath="guide/editable/markup-schema" />

## JSON Schema 案例

<dumi-previewer demoPath="guide/editable/json-schema" />

## Template 案例

<dumi-previewer demoPath="guide/editable/template" />

## API

### Editable

> 内联编辑

参考 [/guide/form-item.html#api](/guide/form-item.html#api)

### Editable.Popover

> 浮层编辑

参考 [https://element-plus.gitee.io/zh-CN/component/popover.html](https://element-plus.gitee.io/zh-CN/component/popover.html)

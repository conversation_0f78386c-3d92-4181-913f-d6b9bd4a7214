# Upload

> 上传组件
>
> 注意：使用上传组件，推荐用户进行二次封装，用户无需关心上传组件与 Formily 的数据通信，只需要处理样式与基本上传配置即可。

## Markup Schema 案例

<dumi-previewer demoPath="guide/upload/markup-schema" />

## JSON Schema 案例

<dumi-previewer demoPath="guide/upload/json-schema" />

## Template 案例

<dumi-previewer demoPath="guide/upload/template" />

## API

参考 [https://element-plus.gitee.io/zh-CN/component/upload.html](https://element-plus.gitee.io/zh-CN/component/upload.html)

.navbar {
  padding: 0 28px !important;
}

.navbar .logo {
  height: auto !important;
  width: 150px !important;
}

.navbar .site-name {
  // display: none;
}

.navbar .sidebar-button {
  padding: 0;
}

.home .feature {
  margin-bottom: 40px;
  text-align: center;
}

.theme-dumi-content:not(.custom) {
  max-width: 100%;
}

.page .page-nav {
  max-width: 100%;
}

.dumi-previewer .dumi-previewer-actions .dumi-previewer-actions__icon {
  padding: 0 !important;
}

.page .page-edit {
  max-width 100%
}

.sidebar-group .sidebar-heading {
  color: #454d64;
  font-size: 16px;
}

.sidebar-group a.sidebar-link {
  font-size: 0.9em;
}

.theme-dumi-content .custom-block.warning {
  padding: 10px 20px;
  border-color: #FFC11F;
  box-shadow: 0 6px 16px -2px rgba(0,0,0,.06);
  background: rgba(255,229,100,0.1);
}
.theme-dumi-content .custom-block.danger {
  padding: 10px 20px;
  p {
    margin: 0;
  }
}

.theme-dumi-content:not(.custom) > h1, .theme-dumi-content:not(.custom) > h2, .theme-dumi-content:not(.custom) > h3, .theme-dumi-content:not(.custom) > h4, .theme-dumi-content:not(.custom) > h5, .theme-dumi-content:not(.custom) > h6 {
  margin-bottom: 18px;
}

.theme-dumi-content p {
  margin: 1em 0;
}

.custom-block.warning p {
  margin: 0;
}

// .theme-dumi-content div[class*="language-"] {
//   background-color: #f9fafb;
// }

// .theme-dumi-content pre[class*="language-"] code {
//   color: #000;
// }

.dumi-previewer .dumi-previewer-source,
.dumi-previewer .dumi-previewer-demo {
  overflow: auto;
}

@media (max-width: 719px) {
  .sidebar-button + .home-link {
    margin-left: 20px;
  }
}

@media (max-width: 419px) {
  .theme-dumi-content div[class*="language-"] {
    margin: 0;
    border-radius: 0;
  }
}


{"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-recommended", "plugin:@typescript-eslint/recommended", "prettier/@typescript-eslint"], "globals": {"sleep": true, "prettyFormat": true}, "parserOptions": {"ecmaVersion": 10, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier", "markdown"], "rules": {"@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/ban-ts-comment": "off", "vue/one-component-per-file": "off"}, "overrides": [{"files": ["**/*.md"], "processor": "markdown/markdown"}, {"files": ["**/*.md/*.{jsx,tsx}"], "rules": {"@typescript-eslint/no-unused-vars": "error", "no-unused-vars": "error", "no-console": "off"}}, {"files": ["**/*.md/*.{js,ts}"], "rules": {"@typescript-eslint/no-unused-vars": "off", "no-unused-vars": "off", "no-console": "off"}}]}
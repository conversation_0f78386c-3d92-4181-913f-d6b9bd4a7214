{"name": "root", "private": true, "devEngines": {"node": "8.x || 9.x || 10.x || 11.x"}, "workspaces": ["packages/*", "playground"], "scripts": {"start": "vuepress dev docs", "build": "formily-tpl build", "build:docs": "vuepress build docs", "test": "jest --coverage", "test:prod": "jest --coverage --silent", "preversion": "yarn install --ignore-engines && npm run build && npm run lint", "version:alpha": "lerna version prerelease --preid alpha", "version:beta": "lerna version prerelease --preid beta", "version:rc": "lerna version prerelease --preid rc", "version:patch": "lerna version patch", "version:minor": "lerna version minor", "version:preminor": "lerna version preminor --preid beta", "version:major": "lerna version major", "release": "lerna publish from-package --yes", "lint": "eslint .idea", "serve:playgroud": "cd playground && yarn serve", "docs:playground": "cd playground && yarn build"}, "devDependencies": {"@element-plus/icons-vue": "^2.0.4", "@formily/core": "^2.2.1", "@formily/grid": "^2.2.1", "@formily/json-schema": "^2.2.1", "@formily/reactive": "^2.2.1", "@formily/reactive-vue": "^2.2.1", "@formily/shared": "^2.2.1", "@formily/validator": "^2.2.1", "@formily/vue": "^2.2.1", "element-plus": "^2.2.14", "@commitlint/cli": "^14.1.0", "@commitlint/config-conventional": "^14.1.0", "@commitlint/prompt-cli": "^14.1.0", "@formily/template": "^1.0.0-alpha.20", "@testing-library/jest-dom": "^5.0.0", "@testing-library/vue": "^5.6.2", "@types/jest": "^24.0.18", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.8.2", "@vitejs/plugin-vue-jsx": "^1.3.8", "@vue/component-compiler-utils": "^3.3.0", "@vue/test-utils": "1.0.0-beta.22", "codesandbox": "^2.2.3", "copy-to-clipboard": "^3.3.1", "core-js": "^2.4.0", "eslint": "^7.14.0", "eslint-config-prettier": "^7.0.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-markdown": "^2.0.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-promise": "^4.0.0", "eslint-plugin-vue": "^8.5.0", "ghooks": "^2.0.4", "html-webpack-plugin": "^3.2.0", "jest": "^27.5.1", "jest-dom": "^4.0.0", "lerna": "^4.0.0", "lint-staged": "^8.2.1", "prettier": "^2.2.1", "pretty-quick": "^3.1.0", "raw-loader": "^4.0.0", "resize-observer-polyfill": "^1.5.1", "rollup": "^2.70.1", "rollup-plugin-dts": "^4.2.0", "rollup-plugin-external-globals": "^0.6.1", "rollup-plugin-inject-process-env": "^1.3.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "sass": "^1.34.1", "sass-loader": "^8.0.2", "stylus": "^0.56.0", "ts-import-plugin": "^2.0.0", "ts-jest": "^26.0.0", "ts-node": "^9.1.1", "ttypescript": "^1.5.13", "typescript": "^4.1.5", "vue": "^3.2.24", "vue-loader": "^16.8.3", "vuepress": "^2.0.0-beta.38", "vuepress-plugin-typescript": "^0.3.1", "vuepress-theme-dumi": "^0.1.1", "webpack-dev-server": "^3.5.1", "@guolao/vue-monaco-editor": "^0.0.5"}, "repository": {"type": "git", "url": "git+https://github.com/formilyjs/element-plus.git"}, "config": {"ghooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint --edit"}}, "lint-staged": {"*.{ts,tsx,js}": ["eslint --ext .ts,.tsx,.js", "pretty-quick --staged", "git add"], "*.md": ["pretty-quick --staged", "git add"]}, "peerDependencies": {"vue": "^3.2.24"}}